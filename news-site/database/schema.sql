-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret';

-- <PERSON><PERSON> custom types
CREATE TYPE user_role AS ENUM ('admin', 'superadmin');
CREATE TYPE article_status AS ENUM ('pending', 'approved', 'rejected');
CREATE TYPE article_type AS ENUM ('regular', 'cutout');

-- Users table (for admins and superadmin)
CREATE TABLE users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    role user_role NOT NULL DEFAULT 'admin',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- News articles table
CREATE TABLE news_articles (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    image_url TEXT NOT NULL,
    type article_type NOT NULL DEFAULT 'regular',
    status article_status NOT NULL DEFAULT 'pending',
    admin_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    approved_by UUID REFERENCES users(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Advertisements table
CREATE TABLE advertisements (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT NOT NULL,
    image_url TEXT NOT NULL,
    redirect_url TEXT NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_news_articles_status ON news_articles(status);
CREATE INDEX idx_news_articles_admin_id ON news_articles(admin_id);
CREATE INDEX idx_news_articles_created_at ON news_articles(created_at DESC);
CREATE INDEX idx_advertisements_is_active ON advertisements(is_active);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_news_articles_updated_at BEFORE UPDATE ON news_articles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_advertisements_updated_at BEFORE UPDATE ON advertisements
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE news_articles ENABLE ROW LEVEL SECURITY;
ALTER TABLE advertisements ENABLE ROW LEVEL SECURITY;

-- RLS Policies for users table
CREATE POLICY "Users can view their own profile" ON users
    FOR SELECT USING (auth.uid()::text = id::text);

CREATE POLICY "Superadmins can view all users" ON users
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id::text = auth.uid()::text AND role = 'superadmin'
        )
    );

CREATE POLICY "Superadmins can insert users" ON users
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id::text = auth.uid()::text AND role = 'superadmin'
        )
    );

CREATE POLICY "Superadmins can update users" ON users
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id::text = auth.uid()::text AND role = 'superadmin'
        )
    );

-- RLS Policies for news_articles table
CREATE POLICY "Anyone can view approved articles" ON news_articles
    FOR SELECT USING (status = 'approved');

CREATE POLICY "Admins can view their own articles" ON news_articles
    FOR SELECT USING (admin_id::text = auth.uid()::text);

CREATE POLICY "Superadmins can view all articles" ON news_articles
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id::text = auth.uid()::text AND role = 'superadmin'
        )
    );

CREATE POLICY "Admins can insert their own articles" ON news_articles
    FOR INSERT WITH CHECK (admin_id::text = auth.uid()::text);

CREATE POLICY "Admins can update their own pending articles" ON news_articles
    FOR UPDATE USING (
        admin_id::text = auth.uid()::text AND status = 'pending'
    );

CREATE POLICY "Superadmins can update any article" ON news_articles
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id::text = auth.uid()::text AND role = 'superadmin'
        )
    );

-- RLS Policies for advertisements table
CREATE POLICY "Anyone can view active advertisements" ON advertisements
    FOR SELECT USING (is_active = true);

CREATE POLICY "Superadmins can manage advertisements" ON advertisements
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id::text = auth.uid()::text AND role = 'superadmin'
        )
    );

-- Insert default superadmin user (password: admin123)
-- Note: In production, use a secure password hash
INSERT INTO users (username, password_hash, role) VALUES 
('superadmin', '$2b$10$rQZ8qVZ8qVZ8qVZ8qVZ8qO', 'superadmin');

-- Create storage bucket for images
INSERT INTO storage.buckets (id, name, public) VALUES ('news-images', 'news-images', true);

-- Storage policies
CREATE POLICY "Anyone can view images" ON storage.objects
    FOR SELECT USING (bucket_id = 'news-images');

CREATE POLICY "Authenticated users can upload images" ON storage.objects
    FOR INSERT WITH CHECK (
        bucket_id = 'news-images' AND 
        auth.role() = 'authenticated'
    );

CREATE POLICY "Users can update their own images" ON storage.objects
    FOR UPDATE USING (
        bucket_id = 'news-images' AND 
        auth.uid()::text = owner::text
    );

CREATE POLICY "Users can delete their own images" ON storage.objects
    FOR DELETE USING (
        bucket_id = 'news-images' AND 
        auth.uid()::text = owner::text
    );
