'use client'

import { useState, useEffect } from 'react'
import { 
  FileText, 
  Clock, 
  CheckCircle, 
  XCircle, 
  TrendingUp,
  Users,
  Eye,
  Calendar
} from 'lucide-react'
import { getCurrentUser } from '@/lib/auth'
import { supabase } from '@/lib/supabase'

interface DashboardStats {
  totalArticles: number
  pendingArticles: number
  approvedArticles: number
  rejectedArticles: number
  totalViews: number
  todayArticles: number
}

export default function AdminDashboard() {
  const [user, setUser] = useState<any>(null)
  const [stats, setStats] = useState<DashboardStats>({
    totalArticles: 0,
    pendingArticles: 0,
    approvedArticles: 0,
    rejectedArticles: 0,
    totalViews: 0,
    todayArticles: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const currentUser = getCurrentUser()
    setUser(currentUser)
    if (currentUser) {
      fetchDashboardStats(currentUser)
    }
  }, [])

  const fetchDashboardStats = async (currentUser: any) => {
    try {
      setLoading(true)
      
      // Fetch user's articles
      const { data: articles, error } = await supabase
        .from('news_articles')
        .select('*')
        .eq('admin_id', currentUser.id)

      if (error) throw error

      const today = new Date().toISOString().split('T')[0]
      const todayArticles = articles?.filter(article => 
        article.created_at.startsWith(today)
      ).length || 0

      const pendingCount = articles?.filter(article => article.status === 'pending').length || 0
      const approvedCount = articles?.filter(article => article.status === 'approved').length || 0
      const rejectedCount = articles?.filter(article => article.status === 'rejected').length || 0

      setStats({
        totalArticles: articles?.length || 0,
        pendingArticles: pendingCount,
        approvedArticles: approvedCount,
        rejectedArticles: rejectedCount,
        totalViews: Math.floor(Math.random() * 10000) + 1000, // Mock data
        todayArticles
      })
    } catch (error) {
      console.error('Error fetching dashboard stats:', error)
    } finally {
      setLoading(false)
    }
  }

  const statCards = [
    {
      title: 'Total Articles',
      value: stats.totalArticles,
      icon: FileText,
      color: 'blue',
      change: '+12%'
    },
    {
      title: 'Pending Review',
      value: stats.pendingArticles,
      icon: Clock,
      color: 'yellow',
      change: '+3'
    },
    {
      title: 'Approved',
      value: stats.approvedArticles,
      icon: CheckCircle,
      color: 'green',
      change: '+8%'
    },
    {
      title: 'Total Views',
      value: stats.totalViews.toLocaleString(),
      icon: Eye,
      color: 'purple',
      change: '+15%'
    }
  ]

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'bg-blue-50 text-blue-600 border-blue-200',
      yellow: 'bg-yellow-50 text-yellow-600 border-yellow-200',
      green: 'bg-green-50 text-green-600 border-green-200',
      purple: 'bg-purple-50 text-purple-600 border-purple-200'
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white p-6 rounded-lg shadow-sm border animate-pulse">
              <div className="h-4 bg-gray-200 rounded mb-4"></div>
              <div className="h-8 bg-gray-200 rounded mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Welcome Header */}
      <div className="bg-white p-6 rounded-lg shadow-sm border">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Welcome back, {user?.username}!
            </h1>
            <p className="text-gray-600 mt-1">
              Here's what's happening with your content today.
            </p>
          </div>
          <div className="text-right">
            <p className="text-sm text-gray-500">Today</p>
            <p className="text-lg font-semibold text-gray-900">
              {new Date().toLocaleDateString('en-US', { 
                weekday: 'long', 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
          </div>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((stat) => {
          const Icon = stat.icon
          return (
            <div key={stat.title} className="bg-white p-6 rounded-lg shadow-sm border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                  <p className="text-2xl font-bold text-gray-900 mt-1">{stat.value}</p>
                </div>
                <div className={`p-3 rounded-lg ${getColorClasses(stat.color)}`}>
                  <Icon size={24} />
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <TrendingUp size={16} className="text-green-500 mr-1" />
                <span className="text-sm text-green-600 font-medium">{stat.change}</span>
                <span className="text-sm text-gray-500 ml-1">from last month</span>
              </div>
            </div>
          )
        })}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Activity */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
          <div className="space-y-4">
            {stats.todayArticles > 0 ? (
              <div className="flex items-center space-x-3 p-3 bg-green-50 rounded-lg">
                <CheckCircle className="text-green-600" size={20} />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {stats.todayArticles} article{stats.todayArticles > 1 ? 's' : ''} uploaded today
                  </p>
                  <p className="text-xs text-gray-500">Great job staying active!</p>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-3 p-3 bg-blue-50 rounded-lg">
                <Calendar className="text-blue-600" size={20} />
                <div>
                  <p className="text-sm font-medium text-gray-900">No articles uploaded today</p>
                  <p className="text-xs text-gray-500">Ready to share some news?</p>
                </div>
              </div>
            )}
            
            {stats.pendingArticles > 0 && (
              <div className="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg">
                <Clock className="text-yellow-600" size={20} />
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {stats.pendingArticles} article{stats.pendingArticles > 1 ? 's' : ''} pending review
                  </p>
                  <p className="text-xs text-gray-500">Waiting for superadmin approval</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <a
              href="/admin/dashboard/upload"
              className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="p-2 bg-blue-100 rounded-lg">
                <FileText className="text-blue-600" size={16} />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">Upload New Article</p>
                <p className="text-xs text-gray-500">Share regular news or newspaper cutouts</p>
              </div>
            </a>
            
            <a
              href="/admin/dashboard/articles"
              className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <div className="p-2 bg-green-100 rounded-lg">
                <Eye className="text-green-600" size={16} />
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">View My Articles</p>
                <p className="text-xs text-gray-500">Manage your published content</p>
              </div>
            </a>

            {user?.role === 'superadmin' && (
              <a
                href="/admin/dashboard/review"
                className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="p-2 bg-purple-100 rounded-lg">
                  <Users className="text-purple-600" size={16} />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-900">Review Articles</p>
                  <p className="text-xs text-gray-500">Approve or reject pending submissions</p>
                </div>
              </a>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
