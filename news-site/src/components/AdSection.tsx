'use client'

import { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { ExternalLink } from 'lucide-react'
import { supabase, Advertisement } from '@/lib/supabase'

export default function AdSection() {
  const [ads, setAds] = useState<Advertisement[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchActiveAds()
  }, [])

  const fetchActiveAds = async () => {
    try {
      const { data, error } = await supabase
        .from('advertisements')
        .select('*')
        .eq('is_active', true)
        .order('created_at', { ascending: false })

      if (error) throw error
      setAds(data || [])
    } catch (err) {
      setError('Failed to load advertisements')
      console.error('Error fetching ads:', err)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="card p-4 animate-pulse">
            <div className="w-full h-32 bg-gray-200 rounded-lg mb-3"></div>
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-3/4"></div>
          </div>
        ))}
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <div className="text-red-500 mb-2">
          <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <p className="text-sm text-gray-600 mb-3">{error}</p>
        <button 
          onClick={fetchActiveAds}
          className="text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 transition-colors"
        >
          Retry
        </button>
      </div>
    )
  }

  if (ads.length === 0) {
    return (
      <div className="text-center py-8">
        <div className="text-gray-400 mb-2">
          <svg className="w-8 h-8 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </div>
        <p className="text-sm text-gray-600">No advertisements available</p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {ads.map((ad) => (
        <Link
          key={ad.id}
          href={ad.redirect_url}
          target="_blank"
          rel="noopener noreferrer"
          className="block card overflow-hidden hover:shadow-lg transition-all duration-200 group"
        >
          <div className="relative h-32 w-full">
            <Image
              src={ad.image_url}
              alt={ad.title}
              fill
              className="object-cover group-hover:scale-105 transition-transform duration-200"
              sizes="(max-width: 768px) 100vw, 300px"
            />
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200"></div>
            <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
              <div className="bg-white bg-opacity-90 rounded-full p-1">
                <ExternalLink size={12} className="text-gray-700" />
              </div>
            </div>
          </div>
          
          <div className="p-4">
            <h3 className="font-semibold text-gray-900 text-sm mb-1 line-clamp-2 group-hover:text-blue-600 transition-colors">
              {ad.title}
            </h3>
            <p className="text-gray-600 text-xs line-clamp-2">
              {ad.description}
            </p>
            
            <div className="mt-2 flex items-center justify-between">
              <span className="text-xs text-blue-600 font-medium">
                Learn More
              </span>
              <div className="text-xs text-gray-400">
                Sponsored
              </div>
            </div>
          </div>
        </Link>
      ))}
      
      {/* Ad Disclaimer */}
      <div className="text-center py-3 border-t border-gray-200">
        <p className="text-xs text-gray-500">
          Advertisements are provided by third parties
        </p>
      </div>
    </div>
  )
}
