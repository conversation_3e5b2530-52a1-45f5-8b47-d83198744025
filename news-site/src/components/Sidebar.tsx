'use client'

import Link from 'next/link'
import { 
  Newspaper, 
  GraduationCap, 
  Home as HomeIcon, 
  Car,
  ExternalLink 
} from 'lucide-react'

const sidebarLinks = [
  {
    name: 'News',
    href: '/',
    icon: Newspaper,
    description: 'Latest news and updates',
    isActive: true
  },
  {
    name: 'Courses',
    href: '/courses',
    icon: GraduationCap,
    description: 'Educational courses and training',
    isExternal: true
  },
  {
    name: 'Vastu',
    href: '/vastu',
    icon: HomeIcon,
    description: 'Vastu consultation and guidance',
    isExternal: true
  },
  {
    name: 'Rental',
    href: '/rental',
    icon: Car,
    description: 'Vehicle and property rentals',
    isExternal: true
  }
]

export default function Sidebar() {
  return (
    <aside className="w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen">
      <div className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">
          Our Platforms
        </h2>
        
        <nav className="space-y-2">
          {sidebarLinks.map((link) => {
            const Icon = link.icon
            
            return (
              <Link
                key={link.name}
                href={link.href}
                className={`
                  flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 group
                  ${link.isActive 
                    ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                    : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                  }
                `}
                target={link.isExternal ? '_blank' : '_self'}
                rel={link.isExternal ? 'noopener noreferrer' : undefined}
              >
                <div className={`
                  p-2 rounded-md transition-colors
                  ${link.isActive 
                    ? 'bg-blue-100 text-blue-700' 
                    : 'bg-gray-100 text-gray-600 group-hover:bg-blue-100 group-hover:text-blue-600'
                  }
                `}>
                  <Icon size={16} />
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium truncate">
                      {link.name}
                    </span>
                    {link.isExternal && (
                      <ExternalLink size={12} className="text-gray-400" />
                    )}
                  </div>
                  <p className="text-xs text-gray-500 truncate">
                    {link.description}
                  </p>
                </div>
              </Link>
            )
          })}
        </nav>

        {/* Additional Info Section */}
        <div className="mt-8 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-sm font-medium text-gray-900 mb-2">
            Stay Connected
          </h3>
          <p className="text-xs text-gray-600 mb-3">
            Follow us for the latest updates and news across all our platforms.
          </p>
          
          <div className="flex space-x-2">
            <button className="flex-1 bg-blue-600 text-white text-xs py-2 px-3 rounded-md hover:bg-blue-700 transition-colors">
              Subscribe
            </button>
            <button className="flex-1 border border-gray-300 text-gray-700 text-xs py-2 px-3 rounded-md hover:bg-gray-50 transition-colors">
              Share
            </button>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="mt-6 space-y-3">
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">Today's Articles</span>
            <span className="font-semibold text-gray-900">12</span>
          </div>
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">Total Views</span>
            <span className="font-semibold text-gray-900">2.4K</span>
          </div>
          <div className="flex justify-between items-center text-sm">
            <span className="text-gray-600">Active Readers</span>
            <span className="font-semibold text-green-600">156</span>
          </div>
        </div>
      </div>
    </aside>
  )
}
