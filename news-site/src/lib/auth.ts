import bcrypt from 'bcryptjs'
import { supabase } from './supabase'
import { User } from './supabase'

export interface LoginCredentials {
  username: string
  password: string
}

export interface AuthResponse {
  success: boolean
  user?: User
  error?: string
}

export async function loginUser(credentials: LoginCredentials): Promise<AuthResponse> {
  try {
    const { data: user, error } = await supabase
      .from('users')
      .select('*')
      .eq('username', credentials.username)
      .single()

    if (error || !user) {
      return { success: false, error: 'Invalid username or password' }
    }

    const isValidPassword = await bcrypt.compare(credentials.password, user.password_hash)
    
    if (!isValidPassword) {
      return { success: false, error: 'Invalid username or password' }
    }

    // Create a session token (in a real app, you'd use JWT)
    const sessionToken = generateSessionToken()
    
    // Store session in localStorage (client-side)
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', sessionToken)
      localStorage.setItem('user_data', JSON.stringify({
        id: user.id,
        username: user.username,
        role: user.role
      }))
    }

    return { success: true, user }
  } catch (error) {
    return { success: false, error: 'Login failed' }
  }
}

export async function createUser(username: string, password: string, role: 'admin' | 'superadmin' = 'admin'): Promise<AuthResponse> {
  try {
    const passwordHash = await bcrypt.hash(password, 10)
    
    const { data: user, error } = await supabase
      .from('users')
      .insert({
        username,
        password_hash: passwordHash,
        role
      })
      .select()
      .single()

    if (error) {
      return { success: false, error: 'Failed to create user' }
    }

    return { success: true, user }
  } catch (error) {
    return { success: false, error: 'Failed to create user' }
  }
}

export function getCurrentUser(): User | null {
  if (typeof window === 'undefined') return null
  
  const userData = localStorage.getItem('user_data')
  const authToken = localStorage.getItem('auth_token')
  
  if (!userData || !authToken) return null
  
  try {
    return JSON.parse(userData)
  } catch {
    return null
  }
}

export function logout(): void {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('auth_token')
    localStorage.removeItem('user_data')
  }
}

export function isAuthenticated(): boolean {
  return getCurrentUser() !== null
}

export function isSuperAdmin(): boolean {
  const user = getCurrentUser()
  return user?.role === 'superadmin'
}

export function isAdmin(): boolean {
  const user = getCurrentUser()
  return user?.role === 'admin' || user?.role === 'superadmin'
}

function generateSessionToken(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36)
}
