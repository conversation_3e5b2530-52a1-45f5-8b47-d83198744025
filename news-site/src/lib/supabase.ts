import { createClient } from '@supabase/supabase-js'
import { createBrowserClient } from '@supabase/ssr'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export function createSupabaseClient() {
  return createBrowserClient(supabaseUrl, supabaseAnonKey)
}

// Database types
export interface User {
  id: string
  username: string
  role: 'admin' | 'superadmin'
  created_at: string
  updated_at: string
}

export interface NewsArticle {
  id: string
  title: string
  description?: string
  image_url: string
  type: 'regular' | 'cutout'
  status: 'pending' | 'approved' | 'rejected'
  admin_id: string
  created_at: string
  updated_at: string
  approved_by?: string
  approved_at?: string
}

export interface Advertisement {
  id: string
  title: string
  description: string
  image_url: string
  redirect_url: string
  is_active: boolean
  created_at: string
  updated_at: string
}
