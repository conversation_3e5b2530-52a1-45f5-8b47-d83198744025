{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_9e72d27f-module__JKMi0a__className\",\n  \"variable\": \"inter_9e72d27f-module__JKMi0a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Coding/NEWS/news-site/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { Inter } from \"next/font/google\";\nimport \"./globals.css\";\nimport { Toaster } from 'react-hot-toast';\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  variable: \"--font-inter\",\n});\n\nexport const metadata: Metadata = {\n  title: \"News Portal - Latest News & Updates\",\n  description: \"Stay updated with the latest news, articles, and newspaper cutouts from trusted sources.\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body className={`${inter.variable} font-sans antialiased bg-gray-50`}>\n        {children}\n        <Toaster\n          position=\"top-right\"\n          toastOptions={{\n            duration: 4000,\n            style: {\n              background: '#363636',\n              color: '#fff',\n            },\n          }}\n        />\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAGA;;;;;AAOO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,iCAAiC,CAAC;;gBAClE;8BACD,8OAAC,uJAAA,CAAA,UAAO;oBACN,UAAS;oBACT,cAAc;wBACZ,UAAU;wBACV,OAAO;4BACL,YAAY;4BACZ,OAAO;wBACT;oBACF;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Coding/NEWS/news-site/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Coding/NEWS/news-site/node_modules/react-hot-toast/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const CheckmarkIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call CheckmarkIcon() from the server but CheckmarkIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"CheckmarkIcon\",\n);\nexport const ErrorIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call ErrorIcon() from the server but ErrorIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"ErrorIcon\",\n);\nexport const LoaderIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call LoaderIcon() from the server but LoaderIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"LoaderIcon\",\n);\nexport const ToastBar = registerClientReference(\n    function() { throw new Error(\"Attempted to call ToastBar() from the server but ToastBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"ToastBar\",\n);\nexport const ToastIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call ToastIcon() from the server but ToastIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"ToastIcon\",\n);\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"Toaster\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"default\",\n);\nexport const resolveValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call resolveValue() from the server but resolveValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"resolveValue\",\n);\nexport const toast = registerClientReference(\n    function() { throw new Error(\"Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"toast\",\n);\nexport const useToaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call useToaster() from the server but useToaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"useToaster\",\n);\nexport const useToasterStore = registerClientReference(\n    function() { throw new Error(\"Attempted to call useToasterStore() from the server but useToasterStore is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs <module evaluation>\",\n    \"useToasterStore\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,6EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,6EACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,6EACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6EACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,6EACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+S,GAC5U,6EACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,6EACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,6EACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,6EACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,6EACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 143, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Coding/NEWS/news-site/node_modules/react-hot-toast/dist/index.mjs/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const CheckmarkIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call CheckmarkIcon() from the server but CheckmarkIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"CheckmarkIcon\",\n);\nexport const ErrorIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call ErrorIcon() from the server but ErrorIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"ErrorIcon\",\n);\nexport const LoaderIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call LoaderIcon() from the server but LoaderIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"LoaderIcon\",\n);\nexport const ToastBar = registerClientReference(\n    function() { throw new Error(\"Attempted to call ToastBar() from the server but ToastBar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"ToastBar\",\n);\nexport const ToastIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call ToastIcon() from the server but ToastIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"ToastIcon\",\n);\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"Toaster\",\n);\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/node_modules/react-hot-toast/dist/index.mjs from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"default\",\n);\nexport const resolveValue = registerClientReference(\n    function() { throw new Error(\"Attempted to call resolveValue() from the server but resolveValue is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"resolveValue\",\n);\nexport const toast = registerClientReference(\n    function() { throw new Error(\"Attempted to call toast() from the server but toast is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"toast\",\n);\nexport const useToaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call useToaster() from the server but useToaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"useToaster\",\n);\nexport const useToasterStore = registerClientReference(\n    function() { throw new Error(\"Attempted to call useToasterStore() from the server but useToasterStore is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/node_modules/react-hot-toast/dist/index.mjs\",\n    \"useToasterStore\",\n);\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yDACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yDACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,yDACA;AAEG,MAAM,WAAW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,yDACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yDACA;AAEG,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,yDACA;uCAEW,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2R,GACxT,yDACA;AAEG,MAAM,eAAe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,yDACA;AAEG,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,yDACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,yDACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,yDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 195, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Coding/NEWS/news-site/node_modules/react-hot-toast/src/core/types.ts", "file:///Users/<USER>/Coding/NEWS/news-site/node_modules/react-hot-toast/src/core/utils.ts", "file:///Users/<USER>/Coding/NEWS/news-site/node_modules/react-hot-toast/src/core/store.ts", "file:///Users/<USER>/Coding/NEWS/news-site/node_modules/react-hot-toast/src/core/toast.ts", "file:///Users/<USER>/Coding/NEWS/news-site/node_modules/react-hot-toast/src/core/use-toaster.ts", "file:///Users/<USER>/Coding/NEWS/news-site/node_modules/react-hot-toast/src/components/toast-bar.tsx", "file:///Users/<USER>/Coding/NEWS/news-site/node_modules/react-hot-toast/src/components/toast-icon.tsx", "file:///Users/<USER>/Coding/NEWS/news-site/node_modules/react-hot-toast/src/components/error.tsx", "file:///Users/<USER>/Coding/NEWS/news-site/node_modules/react-hot-toast/src/components/loader.tsx", "file:///Users/<USER>/Coding/NEWS/news-site/node_modules/react-hot-toast/src/components/checkmark.tsx", "file:///Users/<USER>/Coding/NEWS/news-site/node_modules/react-hot-toast/src/components/toaster.tsx", "file:///Users/<USER>/Coding/NEWS/news-site/node_modules/react-hot-toast/src/index.ts"], "sourcesContent": ["import { CSSProperties } from 'react';\n\nexport type ToastType = 'success' | 'error' | 'loading' | 'blank' | 'custom';\nexport type ToastPosition =\n  | 'top-left'\n  | 'top-center'\n  | 'top-right'\n  | 'bottom-left'\n  | 'bottom-center'\n  | 'bottom-right';\n\nexport type Renderable = React.ReactElement | string | null;\n\nexport interface IconTheme {\n  primary: string;\n  secondary: string;\n}\n\nexport type ValueFunction<TValue, TArg> = (arg: TArg) => TValue;\nexport type ValueOrFunction<TValue, TArg> =\n  | TValue\n  | ValueFunction<TValue, TArg>;\n\nconst isFunction = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>\n): valOrFunction is ValueFunction<TValue, TArg> =>\n  typeof valOrFunction === 'function';\n\nexport const resolveValue = <TValue, TArg>(\n  valOrFunction: ValueOrFunction<TValue, TArg>,\n  arg: TArg\n): TValue => (isFunction(valOrFunction) ? valOrFunction(arg) : valOrFunction);\n\nexport interface Toast {\n  type: ToastType;\n  id: string;\n  message: ValueOrFunction<Renderable, Toast>;\n  icon?: Renderable;\n  duration?: number;\n  pauseDuration: number;\n  position?: ToastPosition;\n  removeDelay?: number;\n\n  ariaProps: {\n    role: 'status' | 'alert';\n    'aria-live': 'assertive' | 'off' | 'polite';\n  };\n\n  style?: CSSProperties;\n  className?: string;\n  iconTheme?: IconTheme;\n\n  createdAt: number;\n  visible: boolean;\n  dismissed: boolean;\n  height?: number;\n}\n\nexport type ToastOptions = Partial<\n  Pick<\n    Toast,\n    | 'id'\n    | 'icon'\n    | 'duration'\n    | 'ariaProps'\n    | 'className'\n    | 'style'\n    | 'position'\n    | 'iconTheme'\n    | 'removeDelay'\n  >\n>;\n\nexport type DefaultToastOptions = ToastOptions & {\n  [key in ToastType]?: ToastOptions;\n};\n\nexport interface ToasterProps {\n  position?: ToastPosition;\n  toastOptions?: DefaultToastOptions;\n  reverseOrder?: boolean;\n  gutter?: number;\n  containerStyle?: React.CSSProperties;\n  containerClassName?: string;\n  children?: (toast: Toast) => React.ReactElement;\n}\n\nexport interface ToastWrapperProps {\n  id: string;\n  className?: string;\n  style?: React.CSSProperties;\n  onHeightUpdate: (id: string, height: number) => void;\n  children?: React.ReactNode;\n}\n", "export const genId = (() => {\n  let count = 0;\n  return () => {\n    return (++count).toString();\n  };\n})();\n\nexport const prefersReducedMotion = (() => {\n  // Cache result\n  let shouldReduceMotion: boolean | undefined = undefined;\n\n  return () => {\n    if (shouldReduceMotion === undefined && typeof window !== 'undefined') {\n      const mediaQuery = matchMedia('(prefers-reduced-motion: reduce)');\n      shouldReduceMotion = !mediaQuery || mediaQuery.matches;\n    }\n    return shouldReduceMotion;\n  };\n})();\n", "import { useEffect, useState, useRef } from 'react';\nimport { DefaultToastOptions, Toast, ToastType } from './types';\n\nconst TOAST_LIMIT = 20;\n\nexport enum ActionType {\n  ADD_TOAST,\n  UPDATE_TOAST,\n  UPSERT_TOAST,\n  DISMISS_TOAST,\n  REMOVE_TOAST,\n  START_PAUSE,\n  END_PAUSE,\n}\n\ntype Action =\n  | {\n      type: ActionType.ADD_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPSERT_TOAST;\n      toast: Toast;\n    }\n  | {\n      type: ActionType.UPDATE_TOAST;\n      toast: Partial<Toast>;\n    }\n  | {\n      type: ActionType.DISMISS_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.REMOVE_TOAST;\n      toastId?: string;\n    }\n  | {\n      type: ActionType.START_PAUSE;\n      time: number;\n    }\n  | {\n      type: ActionType.END_PAUSE;\n      time: number;\n    };\n\ninterface State {\n  toasts: Toast[];\n  pausedAt: number | undefined;\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case ActionType.ADD_TOAST:\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      };\n\n    case ActionType.UPDATE_TOAST:\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      };\n\n    case ActionType.UPSERT_TOAST:\n      const { toast } = action;\n      return reducer(state, {\n        type: state.toasts.find((t) => t.id === toast.id)\n          ? ActionType.UPDATE_TOAST\n          : ActionType.ADD_TOAST,\n        toast,\n      });\n\n    case ActionType.DISMISS_TOAST:\n      const { toastId } = action;\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                dismissed: true,\n                visible: false,\n              }\n            : t\n        ),\n      };\n    case ActionType.REMOVE_TOAST:\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        };\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      };\n\n    case ActionType.START_PAUSE:\n      return {\n        ...state,\n        pausedAt: action.time,\n      };\n\n    case ActionType.END_PAUSE:\n      const diff = action.time - (state.pausedAt || 0);\n\n      return {\n        ...state,\n        pausedAt: undefined,\n        toasts: state.toasts.map((t) => ({\n          ...t,\n          pauseDuration: t.pauseDuration + diff,\n        })),\n      };\n  }\n};\n\nconst listeners: Array<(state: State) => void> = [];\n\nlet memoryState: State = { toasts: [], pausedAt: undefined };\n\nexport const dispatch = (action: Action) => {\n  memoryState = reducer(memoryState, action);\n  listeners.forEach((listener) => {\n    listener(memoryState);\n  });\n};\n\nexport const defaultTimeouts: {\n  [key in ToastType]: number;\n} = {\n  blank: 4000,\n  error: 4000,\n  success: 2000,\n  loading: Infinity,\n  custom: 4000,\n};\n\nexport const useStore = (toastOptions: DefaultToastOptions = {}): State => {\n  const [state, setState] = useState<State>(memoryState);\n  const initial = useRef(memoryState);\n\n  // TODO: Switch to useSyncExternalStore when targeting React 18+\n  useEffect(() => {\n    if (initial.current !== memoryState) {\n      setState(memoryState);\n    }\n    listeners.push(setState);\n    return () => {\n      const index = listeners.indexOf(setState);\n      if (index > -1) {\n        listeners.splice(index, 1);\n      }\n    };\n  }, []);\n\n  const mergedToasts = state.toasts.map((t) => ({\n    ...toastOptions,\n    ...toastOptions[t.type],\n    ...t,\n    removeDelay:\n      t.removeDelay ||\n      toastOptions[t.type]?.removeDelay ||\n      toastOptions?.removeDelay,\n    duration:\n      t.duration ||\n      toastOptions[t.type]?.duration ||\n      toastOptions?.duration ||\n      defaultTimeouts[t.type],\n    style: {\n      ...toastOptions.style,\n      ...toastOptions[t.type]?.style,\n      ...t.style,\n    },\n  }));\n\n  return {\n    ...state,\n    toasts: mergedToasts,\n  };\n};\n", "import {\n  Renderable,\n  Toast,\n  ToastOptions,\n  ToastType,\n  DefaultToastOptions,\n  ValueOrFunction,\n  resolveValue,\n} from './types';\nimport { genId } from './utils';\nimport { dispatch, ActionType } from './store';\n\ntype Message = ValueOrFunction<Renderable, Toast>;\n\ntype ToastHandler = (message: Message, options?: ToastOptions) => string;\n\nconst createToast = (\n  message: Message,\n  type: ToastType = 'blank',\n  opts?: ToastOptions\n): Toast => ({\n  createdAt: Date.now(),\n  visible: true,\n  dismissed: false,\n  type,\n  ariaProps: {\n    role: 'status',\n    'aria-live': 'polite',\n  },\n  message,\n  pauseDuration: 0,\n  ...opts,\n  id: opts?.id || genId(),\n});\n\nconst createHandler =\n  (type?: ToastType): ToastHandler =>\n  (message, options) => {\n    const toast = createToast(message, type, options);\n    dispatch({ type: ActionType.UPSERT_TOAST, toast });\n    return toast.id;\n  };\n\nconst toast = (message: Message, opts?: ToastOptions) =>\n  createHandler('blank')(message, opts);\n\ntoast.error = createHandler('error');\ntoast.success = createHandler('success');\ntoast.loading = createHandler('loading');\ntoast.custom = createHandler('custom');\n\ntoast.dismiss = (toastId?: string) => {\n  dispatch({\n    type: ActionType.DISMISS_TOAST,\n    toastId,\n  });\n};\n\ntoast.remove = (toastId?: string) =>\n  dispatch({ type: ActionType.REMOVE_TOAST, toastId });\n\ntoast.promise = <T>(\n  promise: Promise<T> | (() => Promise<T>),\n  msgs: {\n    loading: Renderable;\n    success?: ValueOrFunction<Renderable, T>;\n    error?: ValueOrFunction<Renderable, any>;\n  },\n  opts?: DefaultToastOptions\n) => {\n  const id = toast.loading(msgs.loading, { ...opts, ...opts?.loading });\n\n  if (typeof promise === 'function') {\n    promise = promise();\n  }\n\n  promise\n    .then((p) => {\n      const successMessage = msgs.success\n        ? resolveValue(msgs.success, p)\n        : undefined;\n\n      if (successMessage) {\n        toast.success(successMessage, {\n          id,\n          ...opts,\n          ...opts?.success,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n      return p;\n    })\n    .catch((e) => {\n      const errorMessage = msgs.error ? resolveValue(msgs.error, e) : undefined;\n\n      if (errorMessage) {\n        toast.error(errorMessage, {\n          id,\n          ...opts,\n          ...opts?.error,\n        });\n      } else {\n        toast.dismiss(id);\n      }\n    });\n\n  return promise;\n};\n\nexport { toast };\n", "import { useEffect, useCallback } from 'react';\nimport { dispatch, ActionType, useStore } from './store';\nimport { toast } from './toast';\nimport { DefaultToastOptions, Toast, ToastPosition } from './types';\n\nconst updateHeight = (toastId: string, height: number) => {\n  dispatch({\n    type: ActionType.UPDATE_TOAST,\n    toast: { id: toastId, height },\n  });\n};\nconst startPause = () => {\n  dispatch({\n    type: ActionType.START_PAUSE,\n    time: Date.now(),\n  });\n};\n\nconst toastTimeouts = new Map<Toast['id'], ReturnType<typeof setTimeout>>();\n\nexport const REMOVE_DELAY = 1000;\n\nconst addToRemoveQueue = (toastId: string, removeDelay = REMOVE_DELAY) => {\n  if (toastTimeouts.has(toastId)) {\n    return;\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId);\n    dispatch({\n      type: ActionType.REMOVE_TOAST,\n      toastId: toastId,\n    });\n  }, removeDelay);\n\n  toastTimeouts.set(toastId, timeout);\n};\n\nexport const useToaster = (toastOptions?: DefaultToastOptions) => {\n  const { toasts, pausedAt } = useStore(toastOptions);\n\n  useEffect(() => {\n    if (pausedAt) {\n      return;\n    }\n\n    const now = Date.now();\n    const timeouts = toasts.map((t) => {\n      if (t.duration === Infinity) {\n        return;\n      }\n\n      const durationLeft =\n        (t.duration || 0) + t.pauseDuration - (now - t.createdAt);\n\n      if (durationLeft < 0) {\n        if (t.visible) {\n          toast.dismiss(t.id);\n        }\n        return;\n      }\n      return setTimeout(() => toast.dismiss(t.id), durationLeft);\n    });\n\n    return () => {\n      timeouts.forEach((timeout) => timeout && clearTimeout(timeout));\n    };\n  }, [toasts, pausedAt]);\n\n  const endPause = useCallback(() => {\n    if (pausedAt) {\n      dispatch({ type: ActionType.END_PAUSE, time: Date.now() });\n    }\n  }, [pausedAt]);\n\n  const calculateOffset = useCallback(\n    (\n      toast: Toast,\n      opts?: {\n        reverseOrder?: boolean;\n        gutter?: number;\n        defaultPosition?: ToastPosition;\n      }\n    ) => {\n      const { reverseOrder = false, gutter = 8, defaultPosition } = opts || {};\n\n      const relevantToasts = toasts.filter(\n        (t) =>\n          (t.position || defaultPosition) ===\n            (toast.position || defaultPosition) && t.height\n      );\n      const toastIndex = relevantToasts.findIndex((t) => t.id === toast.id);\n      const toastsBefore = relevantToasts.filter(\n        (toast, i) => i < toastIndex && toast.visible\n      ).length;\n\n      const offset = relevantToasts\n        .filter((t) => t.visible)\n        .slice(...(reverseOrder ? [toastsBefore + 1] : [0, toastsBefore]))\n        .reduce((acc, t) => acc + (t.height || 0) + gutter, 0);\n\n      return offset;\n    },\n    [toasts]\n  );\n\n  useEffect(() => {\n    // Add dismissed toasts to remove queue\n    toasts.forEach((toast) => {\n      if (toast.dismissed) {\n        addToRemoveQueue(toast.id, toast.removeDelay);\n      } else {\n        // If toast becomes visible again, remove it from the queue\n        const timeout = toastTimeouts.get(toast.id);\n        if (timeout) {\n          clearTimeout(timeout);\n          toastTimeouts.delete(toast.id);\n        }\n      }\n    });\n  }, [toasts]);\n\n  return {\n    toasts,\n    handlers: {\n      updateHeight,\n      startPause,\n      endPause,\n      calculateOffset,\n    },\n  };\n};\n", "import * as React from 'react';\nimport { styled, keyframes } from 'goober';\n\nimport { Toast, ToastPosition, resolveValue, Renderable } from '../core/types';\nimport { ToastIcon } from './toast-icon';\nimport { prefersReducedMotion } from '../core/utils';\n\nconst enterAnimation = (factor: number) => `\n0% {transform: translate3d(0,${factor * -200}%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n`;\n\nconst exitAnimation = (factor: number) => `\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,${factor * -150}%,-1px) scale(.6); opacity:0;}\n`;\n\nconst fadeInAnimation = `0%{opacity:0;} 100%{opacity:1;}`;\nconst fadeOutAnimation = `0%{opacity:1;} 100%{opacity:0;}`;\n\nconst ToastBarBase = styled('div')`\n  display: flex;\n  align-items: center;\n  background: #fff;\n  color: #363636;\n  line-height: 1.3;\n  will-change: transform;\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\n  max-width: 350px;\n  pointer-events: auto;\n  padding: 8px 10px;\n  border-radius: 8px;\n`;\n\nconst Message = styled('div')`\n  display: flex;\n  justify-content: center;\n  margin: 4px 10px;\n  color: inherit;\n  flex: 1 1 auto;\n  white-space: pre-line;\n`;\n\ninterface ToastBarProps {\n  toast: Toast;\n  position?: ToastPosition;\n  style?: React.CSSProperties;\n  children?: (components: {\n    icon: Renderable;\n    message: Renderable;\n  }) => Renderable;\n}\n\nconst getAnimationStyle = (\n  position: ToastPosition,\n  visible: boolean\n): React.CSSProperties => {\n  const top = position.includes('top');\n  const factor = top ? 1 : -1;\n\n  const [enter, exit] = prefersReducedMotion()\n    ? [fadeInAnimation, fadeOutAnimation]\n    : [enterAnimation(factor), exitAnimation(factor)];\n\n  return {\n    animation: visible\n      ? `${keyframes(enter)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`\n      : `${keyframes(exit)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`,\n  };\n};\n\nexport const ToastBar: React.FC<ToastBarProps> = React.memo(\n  ({ toast, position, style, children }) => {\n    const animationStyle: React.CSSProperties = toast.height\n      ? getAnimationStyle(\n          toast.position || position || 'top-center',\n          toast.visible\n        )\n      : { opacity: 0 };\n\n    const icon = <ToastIcon toast={toast} />;\n    const message = (\n      <Message {...toast.ariaProps}>\n        {resolveValue(toast.message, toast)}\n      </Message>\n    );\n\n    return (\n      <ToastBarBase\n        className={toast.className}\n        style={{\n          ...animationStyle,\n          ...style,\n          ...toast.style,\n        }}\n      >\n        {typeof children === 'function' ? (\n          children({\n            icon,\n            message,\n          })\n        ) : (\n          <>\n            {icon}\n            {message}\n          </>\n        )}\n      </ToastBarBase>\n    );\n  }\n);\n", "import * as React from 'react';\nimport { styled, keyframes } from 'goober';\n\nimport { Toast } from '../core/types';\nimport { ErrorIcon, ErrorTheme } from './error';\nimport { LoaderIcon, LoaderTheme } from './loader';\nimport { CheckmarkIcon, CheckmarkTheme } from './checkmark';\n\nconst StatusWrapper = styled('div')`\n  position: absolute;\n`;\n\nconst IndicatorWrapper = styled('div')`\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-width: 20px;\n  min-height: 20px;\n`;\n\nconst enter = keyframes`\nfrom {\n  transform: scale(0.6);\n  opacity: 0.4;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`;\n\nexport const AnimatedIconWrapper = styled('div')`\n  position: relative;\n  transform: scale(0.6);\n  opacity: 0.4;\n  min-width: 20px;\n  animation: ${enter} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n`;\n\nexport type IconThemes = Partial<{\n  success: CheckmarkTheme;\n  error: ErrorTheme;\n  loading: LoaderTheme;\n}>;\n\nexport const ToastIcon: React.FC<{\n  toast: Toast;\n}> = ({ toast }) => {\n  const { icon, type, iconTheme } = toast;\n  if (icon !== undefined) {\n    if (typeof icon === 'string') {\n      return <AnimatedIconWrapper>{icon}</AnimatedIconWrapper>;\n    } else {\n      return icon;\n    }\n  }\n\n  if (type === 'blank') {\n    return null;\n  }\n\n  return (\n    <IndicatorWrapper>\n      <LoaderIcon {...iconTheme} />\n      {type !== 'loading' && (\n        <StatusWrapper>\n          {type === 'error' ? (\n            <ErrorIcon {...iconTheme} />\n          ) : (\n            <CheckmarkIcon {...iconTheme} />\n          )}\n        </StatusWrapper>\n      )}\n    </IndicatorWrapper>\n  );\n};\n", "import { styled, keyframes } from 'goober';\n\nconst circleAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n transform: scale(1) rotate(45deg);\n  opacity: 1;\n}`;\n\nconst firstLineAnimation = keyframes`\nfrom {\n  transform: scale(0);\n  opacity: 0;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}`;\n\nconst secondLineAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(90deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(90deg);\n\topacity: 1;\n}`;\n\nexport interface ErrorTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const ErrorIcon = styled('div')<ErrorTheme>`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${(p) => p.primary || '#ff4b4b'};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${circleAnimation} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n\n  &:after,\n  &:before {\n    content: '';\n    animation: ${firstLineAnimation} 0.15s ease-out forwards;\n    animation-delay: 150ms;\n    position: absolute;\n    border-radius: 3px;\n    opacity: 0;\n    background: ${(p) => p.secondary || '#fff'};\n    bottom: 9px;\n    left: 4px;\n    height: 2px;\n    width: 12px;\n  }\n\n  &:before {\n    animation: ${secondLineAnimation} 0.15s ease-out forwards;\n    animation-delay: 180ms;\n    transform: rotate(90deg);\n  }\n`;\n", "import { styled, keyframes } from 'goober';\n\nconst rotate = keyframes`\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n`;\n\nexport interface LoaderTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const LoaderIcon = styled('div')<LoaderTheme>`\n  width: 12px;\n  height: 12px;\n  box-sizing: border-box;\n  border: 2px solid;\n  border-radius: 100%;\n  border-color: ${(p) => p.secondary || '#e0e0e0'};\n  border-right-color: ${(p) => p.primary || '#616161'};\n  animation: ${rotate} 1s linear infinite;\n`;\n", "import { styled, keyframes } from 'goober';\n\nconst circleAnimation = keyframes`\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(45deg);\n\topacity: 1;\n}`;\n\nconst checkmarkAnimation = keyframes`\n0% {\n\theight: 0;\n\twidth: 0;\n\topacity: 0;\n}\n40% {\n  height: 0;\n\twidth: 6px;\n\topacity: 1;\n}\n100% {\n  opacity: 1;\n  height: 10px;\n}`;\n\nexport interface CheckmarkTheme {\n  primary?: string;\n  secondary?: string;\n}\n\nexport const CheckmarkIcon = styled('div')<CheckmarkTheme>`\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ${(p) => p.primary || '#61d345'};\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: ${circleAnimation} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n  &:after {\n    content: '';\n    box-sizing: border-box;\n    animation: ${checkmarkAnimation} 0.2s ease-out forwards;\n    opacity: 0;\n    animation-delay: 200ms;\n    position: absolute;\n    border-right: 2px solid;\n    border-bottom: 2px solid;\n    border-color: ${(p) => p.secondary || '#fff'};\n    bottom: 6px;\n    left: 6px;\n    height: 10px;\n    width: 6px;\n  }\n`;\n", "import { css, setup } from 'goober';\nimport * as React from 'react';\nimport {\n  resolveValue,\n  ToasterProps,\n  ToastPosition,\n  ToastWrapperProps,\n} from '../core/types';\nimport { useToaster } from '../core/use-toaster';\nimport { prefersReducedMotion } from '../core/utils';\nimport { ToastBar } from './toast-bar';\n\nsetup(React.createElement);\n\nconst ToastWrapper = ({\n  id,\n  className,\n  style,\n  onHeightUpdate,\n  children,\n}: ToastWrapperProps) => {\n  const ref = React.useCallback(\n    (el: HTMLElement | null) => {\n      if (el) {\n        const updateHeight = () => {\n          const height = el.getBoundingClientRect().height;\n          onHeightUpdate(id, height);\n        };\n        updateHeight();\n        new MutationObserver(updateHeight).observe(el, {\n          subtree: true,\n          childList: true,\n          characterData: true,\n        });\n      }\n    },\n    [id, onHeightUpdate]\n  );\n\n  return (\n    <div ref={ref} className={className} style={style}>\n      {children}\n    </div>\n  );\n};\n\nconst getPositionStyle = (\n  position: ToastPosition,\n  offset: number\n): React.CSSProperties => {\n  const top = position.includes('top');\n  const verticalStyle: React.CSSProperties = top ? { top: 0 } : { bottom: 0 };\n  const horizontalStyle: React.CSSProperties = position.includes('center')\n    ? {\n        justifyContent: 'center',\n      }\n    : position.includes('right')\n    ? {\n        justifyContent: 'flex-end',\n      }\n    : {};\n  return {\n    left: 0,\n    right: 0,\n    display: 'flex',\n    position: 'absolute',\n    transition: prefersReducedMotion()\n      ? undefined\n      : `all 230ms cubic-bezier(.21,1.02,.73,1)`,\n    transform: `translateY(${offset * (top ? 1 : -1)}px)`,\n    ...verticalStyle,\n    ...horizontalStyle,\n  };\n};\n\nconst activeClass = css`\n  z-index: 9999;\n  > * {\n    pointer-events: auto;\n  }\n`;\n\nconst DEFAULT_OFFSET = 16;\n\nexport const Toaster: React.FC<ToasterProps> = ({\n  reverseOrder,\n  position = 'top-center',\n  toastOptions,\n  gutter,\n  children,\n  containerStyle,\n  containerClassName,\n}) => {\n  const { toasts, handlers } = useToaster(toastOptions);\n\n  return (\n    <div\n      id=\"_rht_toaster\"\n      style={{\n        position: 'fixed',\n        zIndex: 9999,\n        top: DEFAULT_OFFSET,\n        left: DEFAULT_OFFSET,\n        right: DEFAULT_OFFSET,\n        bottom: DEFAULT_OFFSET,\n        pointerEvents: 'none',\n        ...containerStyle,\n      }}\n      className={containerClassName}\n      onMouseEnter={handlers.startPause}\n      onMouseLeave={handlers.endPause}\n    >\n      {toasts.map((t) => {\n        const toastPosition = t.position || position;\n        const offset = handlers.calculateOffset(t, {\n          reverseOrder,\n          gutter,\n          defaultPosition: position,\n        });\n        const positionStyle = getPositionStyle(toastPosition, offset);\n\n        return (\n          <ToastWrapper\n            id={t.id}\n            key={t.id}\n            onHeightUpdate={handlers.updateHeight}\n            className={t.visible ? activeClass : ''}\n            style={positionStyle}\n          >\n            {t.type === 'custom' ? (\n              resolveValue(t.message, t)\n            ) : children ? (\n              children(t)\n            ) : (\n              <ToastBar toast={t} position={toastPosition} />\n            )}\n          </ToastWrapper>\n        );\n      })}\n    </div>\n  );\n};\n", "import { toast } from './core/toast';\n\nexport * from './headless';\n\nexport { ToastBar } from './components/toast-bar';\nexport { ToastIcon } from './components/toast-icon';\nexport { Toaster } from './components/toaster';\nexport { CheckmarkIcon } from './components/checkmark';\nexport { ErrorIcon } from './components/error';\nexport { LoaderIcon } from './components/loader';\n\nexport { toast };\nexport default toast;\n"], "names": ["isFunction", "valOrFunction", "resolveValue", "arg", "genId", "count", "prefersReducedMotion", "shouldReduceMotion", "mediaQuery", "useEffect", "useState", "useRef", "TOAST_LIMIT", "reducer", "state", "action", "TOAST_LIMIT", "t", "toast", "toastId", "diff", "listeners", "memoryState", "dispatch", "listener", "defaultTimeouts", "useStore", "toastOptions", "setState", "useState", "initial", "useRef", "useEffect", "index", "mergedToasts", "_a", "_b", "_c", "createToast", "message", "type", "opts", "genId", "createHandler", "options", "toast", "dispatch", "toastId", "promise", "msgs", "id", "p", "successMessage", "resolveValue", "e", "errorMessage", "useEffect", "useCallback", "updateHeight", "toastId", "height", "dispatch", "startPause", "toastTimeouts", "REMOVE_DELAY", "addToRemoveQueue", "<PERSON><PERSON><PERSON><PERSON>", "timeout", "useToaster", "toastOptions", "toasts", "pausedAt", "useStore", "useEffect", "now", "timeouts", "t", "durationLeft", "toast", "endPause", "useCallback", "calculateOffset", "opts", "reverseOrder", "gutter", "defaultPosition", "relevantToasts", "toastIndex", "toastsBefore", "i", "acc", "React", "styled", "keyframes", "React", "styled", "keyframes", "styled", "keyframes", "circleAnimation", "firstLineAnimation", "secondLineAnimation", "ErrorIcon", "p", "styled", "keyframes", "rotate", "LoaderIcon", "p", "styled", "keyframes", "circleAnimation", "checkmarkAnimation", "CheckmarkIcon", "p", "StatusWrapper", "styled", "IndicatorWrapper", "enter", "keyframes", "AnimatedIconWrapper", "ToastIcon", "toast", "icon", "type", "iconTheme", "LoaderIcon", "ErrorIcon", "CheckmarkIcon", "enterAnimation", "factor", "exitAnimation", "fadeInAnimation", "fadeOutAnimation", "ToastBarBase", "styled", "Message", "getAnimationStyle", "position", "visible", "enter", "exit", "prefersReducedMotion", "keyframes", "ToastBar", "toast", "style", "children", "animationStyle", "icon", "ToastIcon", "message", "resolveValue", "css", "setup", "React", "setup", "ToastWrapper", "id", "className", "style", "onHeightUpdate", "children", "ref", "el", "updateHeight", "height", "getPositionStyle", "position", "offset", "top", "verticalStyle", "horizontalStyle", "prefersReducedMotion", "activeClass", "css", "DEFAULT_OFFSET", "Toaster", "reverseOrder", "toastOptions", "gutter", "containerStyle", "containerClassName", "toasts", "handlers", "useToaster", "t", "toastPosition", "positionStyle", "resolveValue", "ToastBar", "src_default", "toast"], "mappings": "", "debugId": null}}]}