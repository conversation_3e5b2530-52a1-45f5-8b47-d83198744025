{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_82ad035c._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_fb905aa2.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/admin/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "dh8f0W97x26qz68W/I0eeAuMewMtoaiyZRMGv7t07ic=", "__NEXT_PREVIEW_MODE_ID": "3ea2b76df87e9352168916c69482510c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "91d13a0ef6142915d9e17513824924bd820712c8bc63c426a6c420d0ad6b299d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d76751091d2a784439f5b2abbf97122884ff50b311f52f31ead9c93711bb6661"}}}, "sortedMiddleware": ["/"], "functions": {}}