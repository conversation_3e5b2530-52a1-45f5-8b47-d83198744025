{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\n\nexport function middleware(request: NextRequest) {\n  const { pathname } = request.nextUrl\n\n  // Check if the request is for admin routes\n  if (pathname.startsWith('/admin')) {\n    // Get auth token from cookies or headers\n    const authToken = request.cookies.get('auth_token')?.value || \n                     request.headers.get('authorization')?.replace('Bearer ', '')\n\n    if (!authToken) {\n      // Redirect to login if not authenticated\n      return NextResponse.redirect(new URL('/admin/login', request.url))\n    }\n  }\n\n  return NextResponse.next()\n}\n\nexport const config = {\n  matcher: [\n    '/admin/:path*',\n    '/((?!api|_next/static|_next/image|favicon.ico).*)',\n  ],\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAGO,SAAS,WAAW,OAAoB;IAC7C,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,2CAA2C;IAC3C,IAAI,SAAS,UAAU,CAAC,WAAW;QACjC,yCAAyC;QACzC,MAAM,YAAY,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe,SACpC,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,WAAW;QAE1E,IAAI,CAAC,WAAW;YACd,yCAAyC;YACzC,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,gBAAgB,QAAQ,GAAG;QAClE;IACF;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,SAAS;QACP;QACA;KACD;AACH"}}]}