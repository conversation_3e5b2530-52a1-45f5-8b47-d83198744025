{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Coding/NEWS/news-site/src/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useState } from 'react'\nimport { Menu, X, User } from 'lucide-react'\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n\n  return (\n    <header className=\"bg-white shadow-sm border-b border-gray-200\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo and Brand */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">N</span>\n              </div>\n              <span className=\"text-xl font-bold text-gray-900\">News Portal</span>\n            </Link>\n          </div>\n\n          {/* Navigation Links - Desktop */}\n          <nav className=\"hidden md:flex items-center space-x-8\">\n            <Link \n              href=\"/\" \n              className=\"text-gray-700 hover:text-blue-600 font-medium transition-colors\"\n            >\n              Home\n            </Link>\n            <Link \n              href=\"/categories\" \n              className=\"text-gray-700 hover:text-blue-600 font-medium transition-colors\"\n            >\n              Categories\n            </Link>\n            <Link \n              href=\"/about\" \n              className=\"text-gray-700 hover:text-blue-600 font-medium transition-colors\"\n            >\n              About\n            </Link>\n            <Link \n              href=\"/contact\" \n              className=\"text-gray-700 hover:text-blue-600 font-medium transition-colors\"\n            >\n              Contact\n            </Link>\n          </nav>\n\n          {/* Admin Login Button */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <Link\n              href=\"/admin/login\"\n              className=\"flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              <User size={16} />\n              <span>Admin Login</span>\n            </Link>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=\"text-gray-700 hover:text-blue-600 transition-colors\"\n            >\n              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isMenuOpen && (\n          <div className=\"md:hidden py-4 border-t border-gray-200\">\n            <nav className=\"flex flex-col space-y-4\">\n              <Link \n                href=\"/\" \n                className=\"text-gray-700 hover:text-blue-600 font-medium transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Home\n              </Link>\n              <Link \n                href=\"/categories\" \n                className=\"text-gray-700 hover:text-blue-600 font-medium transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Categories\n              </Link>\n              <Link \n                href=\"/about\" \n                className=\"text-gray-700 hover:text-blue-600 font-medium transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                About\n              </Link>\n              <Link \n                href=\"/contact\" \n                className=\"text-gray-700 hover:text-blue-600 font-medium transition-colors\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                Contact\n              </Link>\n              <Link\n                href=\"/admin/login\"\n                className=\"flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors w-fit\"\n                onClick={() => setIsMenuOpen(false)}\n              >\n                <User size={16} />\n                <span>Admin Login</span>\n              </Link>\n            </nav>\n          </div>\n        )}\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,6LAAC;wCAAK,WAAU;kDAAkC;;;;;;;;;;;;;;;;;sCAKtD,6LAAC;4BAA<PERSON>,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,+JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;sCAMH,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;;kDAEV,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;kDACZ,6LAAC;kDAAK;;;;;;;;;;;;;;;;;sCAKV,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,cAAc,CAAC;gCAC9B,WAAU;0CAET,2BAAa,6LAAC,+LAAA,CAAA,IAAC;oCAAC,MAAM;;;;;yDAAS,6LAAC,qMAAA,CAAA,OAAI;oCAAC,MAAM;;;;;;;;;;;;;;;;;;;;;;gBAMjD,4BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;0CAC9B;;;;;;0CAGD,6LAAC,+JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,cAAc;;kDAE7B,6LAAC,qMAAA,CAAA,OAAI;wCAAC,MAAM;;;;;;kDACZ,6LAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;GAjHwB;KAAA", "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Coding/NEWS/news-site/src/components/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { \n  Newspaper, \n  GraduationCap, \n  Home as HomeIcon, \n  Car,\n  ExternalLink \n} from 'lucide-react'\n\nconst sidebarLinks = [\n  {\n    name: 'News',\n    href: '/',\n    icon: Newspaper,\n    description: 'Latest news and updates',\n    isActive: true\n  },\n  {\n    name: 'Courses',\n    href: '/courses',\n    icon: GraduationCap,\n    description: 'Educational courses and training',\n    isExternal: true\n  },\n  {\n    name: 'Vastu',\n    href: '/vastu',\n    icon: HomeIcon,\n    description: 'Vastu consultation and guidance',\n    isExternal: true\n  },\n  {\n    name: 'Rental',\n    href: '/rental',\n    icon: Car,\n    description: 'Vehicle and property rentals',\n    isExternal: true\n  }\n]\n\nexport default function Sidebar() {\n  return (\n    <aside className=\"w-64 bg-white shadow-sm border-r border-gray-200 min-h-screen\">\n      <div className=\"p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-6\">\n          Our Platforms\n        </h2>\n        \n        <nav className=\"space-y-2\">\n          {sidebarLinks.map((link) => {\n            const Icon = link.icon\n            \n            return (\n              <Link\n                key={link.name}\n                href={link.href}\n                className={`\n                  flex items-center space-x-3 p-3 rounded-lg transition-all duration-200 group\n                  ${link.isActive \n                    ? 'bg-blue-50 text-blue-700 border border-blue-200' \n                    : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'\n                  }\n                `}\n                target={link.isExternal ? '_blank' : '_self'}\n                rel={link.isExternal ? 'noopener noreferrer' : undefined}\n              >\n                <div className={`\n                  p-2 rounded-md transition-colors\n                  ${link.isActive \n                    ? 'bg-blue-100 text-blue-700' \n                    : 'bg-gray-100 text-gray-600 group-hover:bg-blue-100 group-hover:text-blue-600'\n                  }\n                `}>\n                  <Icon size={16} />\n                </div>\n                \n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"font-medium truncate\">\n                      {link.name}\n                    </span>\n                    {link.isExternal && (\n                      <ExternalLink size={12} className=\"text-gray-400\" />\n                    )}\n                  </div>\n                  <p className=\"text-xs text-gray-500 truncate\">\n                    {link.description}\n                  </p>\n                </div>\n              </Link>\n            )\n          })}\n        </nav>\n\n        {/* Additional Info Section */}\n        <div className=\"mt-8 p-4 bg-gray-50 rounded-lg\">\n          <h3 className=\"text-sm font-medium text-gray-900 mb-2\">\n            Stay Connected\n          </h3>\n          <p className=\"text-xs text-gray-600 mb-3\">\n            Follow us for the latest updates and news across all our platforms.\n          </p>\n          \n          <div className=\"flex space-x-2\">\n            <button className=\"flex-1 bg-blue-600 text-white text-xs py-2 px-3 rounded-md hover:bg-blue-700 transition-colors\">\n              Subscribe\n            </button>\n            <button className=\"flex-1 border border-gray-300 text-gray-700 text-xs py-2 px-3 rounded-md hover:bg-gray-50 transition-colors\">\n              Share\n            </button>\n          </div>\n        </div>\n\n        {/* Quick Stats */}\n        <div className=\"mt-6 space-y-3\">\n          <div className=\"flex justify-between items-center text-sm\">\n            <span className=\"text-gray-600\">Today's Articles</span>\n            <span className=\"font-semibold text-gray-900\">12</span>\n          </div>\n          <div className=\"flex justify-between items-center text-sm\">\n            <span className=\"text-gray-600\">Total Views</span>\n            <span className=\"font-semibold text-gray-900\">2.4K</span>\n          </div>\n          <div className=\"flex justify-between items-center text-sm\">\n            <span className=\"text-gray-600\">Active Readers</span>\n            <span className=\"font-semibold text-green-600\">156</span>\n          </div>\n        </div>\n      </div>\n    </aside>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAWA,MAAM,eAAe;IACnB;QACE,MAAM;QACN,MAAM;QACN,MAAM,+MAAA,CAAA,YAAS;QACf,aAAa;QACb,UAAU;IACZ;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,2NAAA,CAAA,gBAAa;QACnB,aAAa;QACb,YAAY;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,OAAQ;QACd,aAAa;QACb,YAAY;IACd;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,mMAAA,CAAA,MAAG;QACT,aAAa;QACb,YAAY;IACd;CACD;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAM,WAAU;kBACf,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAG,WAAU;8BAA2C;;;;;;8BAIzD,6LAAC;oBAAI,WAAU;8BACZ,aAAa,GAAG,CAAC,CAAC;wBACjB,MAAM,OAAO,KAAK,IAAI;wBAEtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,WAAW,AAAC,uHAKT,OAHC,KAAK,QAAQ,GACX,oDACA,sDACH;4BAEH,QAAQ,KAAK,UAAU,GAAG,WAAW;4BACrC,KAAK,KAAK,UAAU,GAAG,wBAAwB;;8CAE/C,6LAAC;oCAAI,WAAW,AAAC,2EAKd,OAHC,KAAK,QAAQ,GACX,8BACA,+EACH;8CAED,cAAA,6LAAC;wCAAK,MAAM;;;;;;;;;;;8CAGd,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DACb,KAAK,IAAI;;;;;;gDAEX,KAAK,UAAU,kBACd,6LAAC,yNAAA,CAAA,eAAY;oDAAC,MAAM;oDAAI,WAAU;;;;;;;;;;;;sDAGtC,6LAAC;4CAAE,WAAU;sDACV,KAAK,WAAW;;;;;;;;;;;;;2BAhChB,KAAK,IAAI;;;;;oBAqCpB;;;;;;8BAIF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6LAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAI1C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;8CAAiG;;;;;;8CAGnH,6LAAC;oCAAO,WAAU;8CAA8G;;;;;;;;;;;;;;;;;;8BAOpI,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;oCAAK,WAAU;8CAA8B;;;;;;;;;;;;sCAEhD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;oCAAK,WAAU;8CAA8B;;;;;;;;;;;;sCAEhD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;oCAAK,WAAU;8CAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3D;KA3FwB", "debugId": null}}, {"offset": {"line": 583, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Coding/NEWS/news-site/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createBrowserClient } from '@supabase/ssr'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\nexport function createSupabaseClient() {\n  return createBrowserClient(supabaseUrl, supabaseAnonKey)\n}\n\n// Database types\nexport interface User {\n  id: string\n  username: string\n  role: 'admin' | 'superadmin'\n  created_at: string\n  updated_at: string\n}\n\nexport interface NewsArticle {\n  id: string\n  title: string\n  description?: string\n  image_url: string\n  type: 'regular' | 'cutout'\n  status: 'pending' | 'approved' | 'rejected'\n  admin_id: string\n  created_at: string\n  updated_at: string\n  approved_by?: string\n  approved_at?: string\n}\n\nexport interface Advertisement {\n  id: string\n  title: string\n  description: string\n  image_url: string\n  redirect_url: string\n  is_active: boolean\n  created_at: string\n  updated_at: string\n}\n"], "names": [], "mappings": ";;;;AAGoB;AAHpB;AACA;AAAA;;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,CAAA,GAAA,0LAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAE3C,SAAS;IACd,OAAO,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;AAC1C", "debugId": null}}, {"offset": {"line": 608, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Coding/NEWS/news-site/src/components/NewsGrid.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Image from 'next/image'\nimport { Calendar, Clock, Tag } from 'lucide-react'\nimport { supabase, NewsArticle } from '@/lib/supabase'\n\nexport default function NewsGrid() {\n  const [articles, setArticles] = useState<NewsArticle[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  useEffect(() => {\n    fetchApprovedArticles()\n  }, [])\n\n  const fetchApprovedArticles = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('news_articles')\n        .select('*')\n        .eq('status', 'approved')\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n      setArticles(data || [])\n    } catch (err) {\n      setError('Failed to load news articles')\n      console.error('Error fetching articles:', err)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const formatDate = (dateString: string) => {\n    const date = new Date(dateString)\n    return date.toLocaleDateString('en-US', {\n      year: 'numeric',\n      month: 'short',\n      day: 'numeric'\n    })\n  }\n\n  const getTimeAgo = (dateString: string) => {\n    const date = new Date(dateString)\n    const now = new Date()\n    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))\n    \n    if (diffInHours < 1) return 'Just now'\n    if (diffInHours < 24) return `${diffInHours}h ago`\n    const diffInDays = Math.floor(diffInHours / 24)\n    if (diffInDays < 7) return `${diffInDays}d ago`\n    return formatDate(dateString)\n  }\n\n  if (loading) {\n    return (\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {[...Array(6)].map((_, i) => (\n          <div key={i} className=\"card p-6 animate-pulse\">\n            <div className=\"w-full h-48 bg-gray-200 rounded-lg mb-4\"></div>\n            <div className=\"h-4 bg-gray-200 rounded mb-2\"></div>\n            <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-4\"></div>\n            <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n          </div>\n        ))}\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"text-red-500 mb-4\">\n          <svg className=\"w-16 h-16 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n          </svg>\n        </div>\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">Failed to load news</h3>\n        <p className=\"text-gray-600 mb-4\">{error}</p>\n        <button \n          onClick={fetchApprovedArticles}\n          className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n        >\n          Try Again\n        </button>\n      </div>\n    )\n  }\n\n  if (articles.length === 0) {\n    return (\n      <div className=\"text-center py-12\">\n        <div className=\"text-gray-400 mb-4\">\n          <svg className=\"w-16 h-16 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3H9M7 16h6M7 8h6v4H7V8z\" />\n          </svg>\n        </div>\n        <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No news articles yet</h3>\n        <p className=\"text-gray-600\">Check back later for the latest updates.</p>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n      {articles.map((article) => (\n        <article key={article.id} className=\"card overflow-hidden hover:shadow-lg transition-shadow\">\n          <div className=\"relative h-48 w-full\">\n            <Image\n              src={article.image_url}\n              alt={article.title}\n              fill\n              className=\"object-cover\"\n              sizes=\"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw\"\n            />\n            <div className=\"absolute top-3 right-3\">\n              <span className={`\n                px-2 py-1 text-xs font-medium rounded-full\n                ${article.type === 'cutout' \n                  ? 'bg-purple-100 text-purple-800' \n                  : 'bg-blue-100 text-blue-800'\n                }\n              `}>\n                {article.type === 'cutout' ? 'Newspaper' : 'Article'}\n              </span>\n            </div>\n          </div>\n          \n          <div className=\"p-6\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-2 line-clamp-2\">\n              {article.title}\n            </h3>\n            \n            {article.description && article.type === 'regular' && (\n              <p className=\"text-gray-600 text-sm mb-4 line-clamp-3\">\n                {article.description}\n              </p>\n            )}\n            \n            <div className=\"flex items-center justify-between text-xs text-gray-500\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"flex items-center space-x-1\">\n                  <Calendar size={12} />\n                  <span>{formatDate(article.created_at)}</span>\n                </div>\n                <div className=\"flex items-center space-x-1\">\n                  <Clock size={12} />\n                  <span>{getTimeAgo(article.created_at)}</span>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-1\">\n                <Tag size={12} />\n                <span className=\"capitalize\">{article.type}</span>\n              </div>\n            </div>\n          </div>\n        </article>\n      ))}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8BAAE;YACR;QACF;6BAAG,EAAE;IAEL,MAAM,wBAAwB;QAC5B,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,iBACL,MAAM,CAAC,KACP,EAAE,CAAC,UAAU,YACb,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YACjB,YAAY,QAAQ,EAAE;QACxB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;YACtC,MAAM;YACN,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,cAAc,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,KAAK,EAAE;QAEjF,IAAI,cAAc,GAAG,OAAO;QAC5B,IAAI,cAAc,IAAI,OAAO,AAAC,GAAc,OAAZ,aAAY;QAC5C,MAAM,aAAa,KAAK,KAAK,CAAC,cAAc;QAC5C,IAAI,aAAa,GAAG,OAAO,AAAC,GAAa,OAAX,YAAW;QACzC,OAAO,WAAW;IACpB;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;oBAAY,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;mBAJP;;;;;;;;;;IASlB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAoB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC3E,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;8BAGzE,6LAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,6LAAC;oBAAE,WAAU;8BAAsB;;;;;;8BACnC,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,IAAI,SAAS,MAAM,KAAK,GAAG;QACzB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAoB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCAC3E,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;8BAGzE,6LAAC;oBAAG,WAAU;8BAAyC;;;;;;8BACvD,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;IAGnC;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;gBAAyB,WAAU;;kCAClC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,gIAAA,CAAA,UAAK;gCACJ,KAAK,QAAQ,SAAS;gCACtB,KAAK,QAAQ,KAAK;gCAClB,IAAI;gCACJ,WAAU;gCACV,OAAM;;;;;;0CAER,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAW,AAAC,iFAKf,OAHC,QAAQ,IAAI,KAAK,WACf,kCACA,6BACH;8CAEA,QAAQ,IAAI,KAAK,WAAW,cAAc;;;;;;;;;;;;;;;;;kCAKjD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,QAAQ,KAAK;;;;;;4BAGf,QAAQ,WAAW,IAAI,QAAQ,IAAI,KAAK,2BACvC,6LAAC;gCAAE,WAAU;0CACV,QAAQ,WAAW;;;;;;0CAIxB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,MAAM;;;;;;kEAChB,6LAAC;kEAAM,WAAW,QAAQ,UAAU;;;;;;;;;;;;0DAEtC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC,uMAAA,CAAA,QAAK;wDAAC,MAAM;;;;;;kEACb,6LAAC;kEAAM,WAAW,QAAQ,UAAU;;;;;;;;;;;;;;;;;;kDAIxC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mMAAA,CAAA,MAAG;gDAAC,MAAM;;;;;;0DACX,6LAAC;gDAAK,WAAU;0DAAc,QAAQ,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;eA/CpC,QAAQ,EAAE;;;;;;;;;;AAuDhC;GA3JwB;KAAA", "debugId": null}}, {"offset": {"line": 1010, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Coding/NEWS/news-site/src/components/AdSection.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Image from 'next/image'\nimport Link from 'next/link'\nimport { ExternalLink } from 'lucide-react'\nimport { supabase, Advertisement } from '@/lib/supabase'\n\nexport default function AdSection() {\n  const [ads, setAds] = useState<Advertisement[]>([])\n  const [loading, setLoading] = useState(true)\n  const [error, setError] = useState<string | null>(null)\n\n  useEffect(() => {\n    fetchActiveAds()\n  }, [])\n\n  const fetchActiveAds = async () => {\n    try {\n      const { data, error } = await supabase\n        .from('advertisements')\n        .select('*')\n        .eq('is_active', true)\n        .order('created_at', { ascending: false })\n\n      if (error) throw error\n      setAds(data || [])\n    } catch (err) {\n      setError('Failed to load advertisements')\n      console.error('Error fetching ads:', err)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"space-y-4\">\n        {[...Array(3)].map((_, i) => (\n          <div key={i} className=\"card p-4 animate-pulse\">\n            <div className=\"w-full h-32 bg-gray-200 rounded-lg mb-3\"></div>\n            <div className=\"h-4 bg-gray-200 rounded mb-2\"></div>\n            <div className=\"h-3 bg-gray-200 rounded w-3/4\"></div>\n          </div>\n        ))}\n      </div>\n    )\n  }\n\n  if (error) {\n    return (\n      <div className=\"text-center py-8\">\n        <div className=\"text-red-500 mb-2\">\n          <svg className=\"w-8 h-8 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n          </svg>\n        </div>\n        <p className=\"text-sm text-gray-600 mb-3\">{error}</p>\n        <button \n          onClick={fetchActiveAds}\n          className=\"text-xs bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700 transition-colors\"\n        >\n          Retry\n        </button>\n      </div>\n    )\n  }\n\n  if (ads.length === 0) {\n    return (\n      <div className=\"text-center py-8\">\n        <div className=\"text-gray-400 mb-2\">\n          <svg className=\"w-8 h-8 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n          </svg>\n        </div>\n        <p className=\"text-sm text-gray-600\">No advertisements available</p>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-4\">\n      {ads.map((ad) => (\n        <Link\n          key={ad.id}\n          href={ad.redirect_url}\n          target=\"_blank\"\n          rel=\"noopener noreferrer\"\n          className=\"block card overflow-hidden hover:shadow-lg transition-all duration-200 group\"\n        >\n          <div className=\"relative h-32 w-full\">\n            <Image\n              src={ad.image_url}\n              alt={ad.title}\n              fill\n              className=\"object-cover group-hover:scale-105 transition-transform duration-200\"\n              sizes=\"(max-width: 768px) 100vw, 300px\"\n            />\n            <div className=\"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200\"></div>\n            <div className=\"absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\n              <div className=\"bg-white bg-opacity-90 rounded-full p-1\">\n                <ExternalLink size={12} className=\"text-gray-700\" />\n              </div>\n            </div>\n          </div>\n          \n          <div className=\"p-4\">\n            <h3 className=\"font-semibold text-gray-900 text-sm mb-1 line-clamp-2 group-hover:text-blue-600 transition-colors\">\n              {ad.title}\n            </h3>\n            <p className=\"text-gray-600 text-xs line-clamp-2\">\n              {ad.description}\n            </p>\n            \n            <div className=\"mt-2 flex items-center justify-between\">\n              <span className=\"text-xs text-blue-600 font-medium\">\n                Learn More\n              </span>\n              <div className=\"text-xs text-gray-400\">\n                Sponsored\n              </div>\n            </div>\n          </div>\n        </Link>\n      ))}\n      \n      {/* Ad Disclaimer */}\n      <div className=\"text-center py-3 border-t border-gray-200\">\n        <p className=\"text-xs text-gray-500\">\n          Advertisements are provided by third parties\n        </p>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,yHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,kBACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM;YAE1C,IAAI,OAAO,MAAM;YACjB,OAAO,QAAQ,EAAE;QACnB,EAAE,OAAO,KAAK;YACZ,SAAS;YACT,QAAQ,KAAK,CAAC,uBAAuB;QACvC,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACZ;mBAAI,MAAM;aAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6LAAC;oBAAY,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;mBAHP;;;;;;;;;;IAQlB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAkB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACzE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;8BAGzE,6LAAC;oBAAE,WAAU;8BAA8B;;;;;;8BAC3C,6LAAC;oBACC,SAAS;oBACT,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,IAAI,IAAI,MAAM,KAAK,GAAG;QACpB,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAkB,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACzE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;8BAGzE,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;IAG3C;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,IAAI,GAAG,CAAC,CAAC,mBACR,6LAAC,+JAAA,CAAA,UAAI;oBAEH,MAAM,GAAG,YAAY;oBACrB,QAAO;oBACP,KAAI;oBACJ,WAAU;;sCAEV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAK,GAAG,SAAS;oCACjB,KAAK,GAAG,KAAK;oCACb,IAAI;oCACJ,WAAU;oCACV,OAAM;;;;;;8CAER,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,yNAAA,CAAA,eAAY;4CAAC,MAAM;4CAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;sCAKxC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,GAAG,KAAK;;;;;;8CAEX,6LAAC;oCAAE,WAAU;8CACV,GAAG,WAAW;;;;;;8CAGjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;sDAGpD,6LAAC;4CAAI,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;mBAlCtC,GAAG,EAAE;;;;;0BA2Cd,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAE,WAAU;8BAAwB;;;;;;;;;;;;;;;;;AAM7C;GA/HwB;KAAA", "debugId": null}}]}